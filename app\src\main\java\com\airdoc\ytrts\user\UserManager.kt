package com.airdoc.ytrts.user

import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.ytrts.user.bean.LoginInfo
import com.airdoc.ytrts.user.bean.User

/**
 * FileName: UserManager
 * Author by lilin,Date on 2025/6/19 11:04
 * PS: Not easy to write code, please indicate.
 */
object UserManager {

    const val PASSWORD_PUBLIC_KEY = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAM51dgYtMyF+tTQt80sfFOpSV27a7t9uaUVeFrdGiVxscuizE7H8SMntYqfn9lp8a5GH5P1/GGehVjUD2gF/4kcCAwEAAQ=="

    //账号正则：4-20个字符，以字母开头，包含数字、下划线，不区分大小写
    val ACCOUNT_REGEX = "^[a-zA-Z][a-zA-Z0-9_]{3,19}$".toRegex()

    //密码正则：8-16个字符，区分大小写，包含数字、特殊字符，至少包含字母和数字
    val PASSWORD_REGEX = "^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z0-9!@#$%^&*()_+\\-=\\[\\]{};':\",./<>?]{8,16}$".toRegex()

    /**
     * 保存用户信息
     */
    fun setUserInfo(user: User?){
        MMKVManager.encodeParcelable(UserPreference.USER_INFO,user)
    }

    /**
     * 获取用户信息
     */
    fun getUserInfo(): User?{
        return MMKVManager.decodeParcelable(UserPreference.USER_INFO, User::class.java)
    }

    /**
     * 保存登录信息
     */
    fun setLoginInfo(loginInfo: LoginInfo?){
        MMKVManager.encodeParcelable(UserPreference.LOGIN_INFO,loginInfo)
    }

    /**
     * 获取登录信息
     */
    fun getLoginInfo(): LoginInfo?{
        return MMKVManager.decodeParcelable(UserPreference.LOGIN_INFO, LoginInfo::class.java)
    }

    /**
     * 获取授权信息
     */
    fun getAuthorization(): String{
        val loginInfo = getLoginInfo()
        return "${loginInfo?.tokenType} ${loginInfo?.token}"
    }

    /**
     * 是否登录
     */
    fun isLogin(): Boolean{
        val loginInfo = getLoginInfo()
        return !loginInfo?.token.isNullOrBlank() && !loginInfo?.tokenType.isNullOrBlank()
    }

    /**
     * 保存上次登录用户名
     */
    fun setLastLoginUserName(username: String){
        MMKVManager.encodeString(UserPreference.LAST_LOGIN_USERNAME,username)
    }

    /**
     * 获取上次登录用户名
     */
    fun getLastLoginUserName(): String?{
        return MMKVManager.decodeString(UserPreference.LAST_LOGIN_USERNAME)
    }

    /**
     * 设置上次登录密码
     */
    fun setLastLoginPassword(password: String){
        MMKVManager.encodeString(UserPreference.LAST_LOGIN_PASSWORD,password)
    }

    /**
     * 获取上次登录密码
     */
    fun getLastLoginPassword(): String?{
        return MMKVManager.decodeString(UserPreference.LAST_LOGIN_PASSWORD)
    }

    /**
     * 是否记住用户名密码
     */
    fun isRememberUsernamePasswordEnable(): Boolean{
        return MMKVManager.decodeBool(UserPreference.REMEMBER_USERNAME_PASSWORD_ENABLE) == true
    }

    /**
     * 设置记住用户名密码
     */
    fun setRememberUsernamePasswordEnable(enable: Boolean){
        MMKVManager.encodeBool(UserPreference.REMEMBER_USERNAME_PASSWORD_ENABLE,enable)
    }

    /**
     * 验证账号格式
     * @param account 账号
     * @return 验证结果，null表示验证通过，否则返回错误信息
     */
    fun validateAccount(account: String): String? {
        return when {
            account.isBlank() -> "账号不能为空"
            account.length < 4 -> "账号长度不能少于4个字符"
            account.length > 20 -> "账号长度不能超过20个字符"
            !account[0].isLetter() -> "账号必须以字母开头"
            !ACCOUNT_REGEX.matches(account) -> "账号只能包含字母、数字和下划线"
            else -> null
        }
    }

    /**
     * 验证密码格式
     * @param password 密码
     * @return 验证结果，null表示验证通过，否则返回错误信息
     */
    fun validatePassword(password: String): String? {
        return when {
            password.isBlank() -> "密码不能为空"
            password.length < 8 -> "密码长度不能少于8个字符"
            password.length > 16 -> "密码长度不能超过16个字符"
            !password.any { it.isLetter() } -> "密码必须包含字母"
            !password.any { it.isDigit() } -> "密码必须包含数字"
            !PASSWORD_REGEX.matches(password) -> "密码包含无效字符或格式不正确"
            else -> null
        }
    }

}