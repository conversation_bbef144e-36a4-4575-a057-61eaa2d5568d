# HRV注册App API调用示例

## 1. 用户登录示例

### 请求示例
```bash
curl -X POST "https://api.hrv-system.com/re-mpd/api/account/login" \
  -H "Content-Type: application/json" \
  -H "X-Device-Sn: DEVICE123456" \
  -H "X-App-Version: 1.0.0" \
  -H "Accept-Language: zh-CN" \
  -d '{
    "username": "doctor001",
    "password": "encrypted_password_hash"
  }'
```

### 响应示例
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************.signature",
    "refreshToken": "refresh_token_string_here",
    "expiresIn": 7200,
    "tokenType": "Bearer"
  }
}
```

## 2. 患者信息查询示例

### 请求示例
```bash
curl -X GET "https://api.hrv-system.com/re-mpd/api/patient/1001" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "X-Device-Sn: DEVICE123456" \
  -H "Accept-Language: zh-CN"
```

### 响应示例
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1001,
    "name": "张三",
    "gender": 1,
    "birthday": "1980-01-01",
    "age": 43,
    "phoneNumber": "138****1234",
    "createTime": "2023-05-01T10:30:00Z",
    "updateTime": "2023-05-15T14:20:00Z",
    "organizationId": 100
  }
}
```

## 3. HRV评估数据上报示例

### 请求示例
```bash
curl -X POST "https://api.hrv-system.com/re-mpd/api/assessment/rppg" \
  -H "Content-Type: application/json;charset=UTF-8" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "X-Device-Sn: DEVICE123456" \
  -d '{
    "patientId": 1001,
    "timeDomain": {
      "meanNN": 850.5,
      "sdnn": 45.2,
      "rmssd": 32.8,
      "sdsd": 28.5,
      "pnn50": 15.6
    },
    "frequencyDomain": {
      "totalPower": 2500.0,
      "vlf": 800.0,
      "lf": 900.0,
      "hf": 800.0,
      "lfHfRatio": 1.125,
      "stepPower": [120.5, 135.2, 145.8, 150.1, 148.9, 142.3, 138.7, 135.4, 132.1, 128.8]
    },
    "rrIntervals": [850, 845, 860, 855, 848, 852, 847, 851, 849, 853],
    "validIntervals": 450,
    "totalIntervals": 500
  }'
```

### 响应示例
```json
{
  "code": 200,
  "message": "上报成功",
  "data": {
    "reportId": "RPT20231201001"
  }
}
```

## 4. 患者列表查询示例

### 请求示例
```bash
curl -X GET "https://api.hrv-system.com/re-mpd/api/patient?page=1&size=10&sort=createTime,desc&gender=1&keywords=张" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "X-Device-Sn: DEVICE123456"
```

### 响应示例
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "id": 1001,
        "name": "张三",
        "gender": 1,
        "birthday": "1980-01-01",
        "age": 43,
        "phoneNumber": "138****1234",
        "createTime": "2023-05-01T10:30:00Z",
        "updateTime": "2023-05-15T14:20:00Z"
      },
      {
        "id": 1002,
        "name": "张四",
        "gender": 1,
        "birthday": "1975-08-15",
        "age": 48,
        "phoneNumber": "139****5678",
        "createTime": "2023-04-28T09:15:00Z",
        "updateTime": "2023-04-28T09:15:00Z"
      }
    ],
    "total": 25
  }
}
```

## 5. 评估报告查询示例

### 请求示例
```bash
curl -X GET "https://api.hrv-system.com/re-mpd/api/assessment?page=1&size=5&sort=createTime,desc" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "X-Device-Sn: DEVICE123456"
```

### 响应示例
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "id": 2001,
        "name": "张三",
        "gender": 1,
        "age": 43,
        "createTime": "2023-05-01T10:30:00Z",
        "patientId": 1001,
        "reportUrl": "https://api.hrv-system.com/reports/RPT20231201001.html"
      },
      {
        "id": 2002,
        "name": "李四",
        "gender": 2,
        "age": 38,
        "createTime": "2023-04-30T15:45:00Z",
        "patientId": 1003,
        "reportUrl": "https://api.hrv-system.com/reports/RPT20231130002.html"
      }
    ],
    "total": 15
  }
}
```

## 6. 错误响应示例

### 401 未授权错误
```json
{
  "code": 401,
  "message": "令牌无效，请重新授权",
  "data": null,
  "timestamp": "2023-05-01T10:30:00Z"
}
```

### 400 参数错误
```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": {
    "errors": [
      {
        "field": "patientId",
        "message": "患者ID不能为空"
      },
      {
        "field": "timeDomain.meanNN",
        "message": "平均RR间期必须大于0"
      }
    ]
  },
  "timestamp": "2023-05-01T10:30:00Z"
}
```

### 429 频率限制错误
```json
{
  "code": 429,
  "message": "请求过于频繁，请 1 分钟后重试",
  "data": {
    "retryAfter": 60,
    "limit": 100,
    "remaining": 0
  },
  "timestamp": "2023-05-01T10:30:00Z"
}
```

## 7. Android客户端调用示例（Kotlin）

```kotlin
// 使用Retrofit进行API调用
class HRVApiClient {
    
    private val retrofit = Retrofit.Builder()
        .baseUrl("https://api.hrv-system.com/")
        .addConverterFactory(GsonConverterFactory.create())
        .build()
    
    private val apiService = retrofit.create(HRVApiService::class.java)
    
    // 上报评估数据
    suspend fun reportEvaluationData(
        result: AnalysisResult,
        authorization: String
    ): ApiResponse<EvaluationReportResult> {
        val requestBody = Gson().toJson(result)
            .toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
        
        return apiService.reportEvaluationData(requestBody, authorization)
    }
    
    // 查询患者信息
    suspend fun queryPatient(
        id: Long,
        authorization: String
    ): ApiResponse<Patient> {
        return apiService.queryPatient(id, authorization)
    }
}
```

## 8. 请求头说明

所有API请求都应包含以下标准请求头：

```
Content-Type: application/json;charset=UTF-8
Authorization: Bearer {access_token}
X-Device-Sn: {device_serial_number}
X-App-Version: {app_version}
X-Device-Mode: {device_model}
X-Airdoc-Client: e4d4b6c3-2b49-440e-98fd-0837a5fcb858
Accept-Language: zh-CN
```

## 9. 数据验证规则

### 患者信息验证
- 姓名：1-50个字符，不能为空
- 性别：1（男）或2（女）
- 生日：YYYY-MM-DD格式
- 手机号：11位数字

### HRV数据验证
- meanNN：300-2000ms范围
- SDNN：0-500ms范围
- RMSSD：0-500ms范围
- 有效间期比例：≥80%
