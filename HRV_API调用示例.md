# HRV注册App API调用示例

## 1. 用户登录示例

### 请求示例
```bash
curl -X POST "https://api.hrv-system.com/re-mpd/api/account/login" \
  -H "Content-Type: application/json" \
  -H "X-Device-Sn: DEVICE123456" \
  -H "X-App-Version: 1.0.0" \
  -H "Accept-Language: zh-CN" \
  -d '{
    "username": "doctor001",
    "password": "encrypted_password_hash"
  }'
```

### 响应示例
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************.signature",
    "refreshToken": "refresh_token_string_here",
    "expiresIn": 7200,
    "tokenType": "Bearer"
  }
}
```

## 2. 患者信息查询示例

### 请求示例
```bash
curl -X GET "https://api.hrv-system.com/re-mpd/api/patient/1001" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "X-Device-Sn: DEVICE123456" \
  -H "Accept-Language: zh-CN"
```

### 响应示例
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1001,
    "name": "张三",
    "gender": 1,
    "birthday": "1980-01-01",
    "age": 43,
    "phoneNumber": "138****1234",
    "createTime": "2023-05-01T10:30:00Z",
    "updateTime": "2023-05-15T14:20:00Z",
    "organizationId": 100
  }
}
```

## 3. HRV评估数据上报示例

### 请求示例
```bash
curl -X POST "https://api.hrv-system.com/re-mpd/api/assessment/rppg" \
  -H "Content-Type: application/json;charset=UTF-8" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "X-Device-Sn: DEVICE123456" \
  -d '{
    "patientId": 1001,
    "timeDomain": {
      "meanNN": 850.5,
      "sdnn": 45.2,
      "rmssd": 32.8,
      "sdsd": 28.5,
      "pnn50": 15.6
    },
    "frequencyDomain": {
      "totalPower": 2500.0,
      "vlf": 800.0,
      "lf": 900.0,
      "hf": 800.0,
      "lfHfRatio": 1.125,
      "stepPower": [120.5, 135.2, 145.8, 150.1, 148.9, 142.3, 138.7, 135.4, 132.1, 128.8]
    },
    "rrIntervals": [850, 845, 860, 855, 848, 852, 847, 851, 849, 853],
    "validIntervals": 450,
    "totalIntervals": 500
  }'
```

### 响应示例
```json
{
  "code": 200,
  "message": "上报成功",
  "data": {
    "reportId": "RPT20231201001"
  }
}
```

## 4. 患者列表查询示例

### 请求示例
```bash
curl -X GET "https://api.hrv-system.com/re-mpd/api/patient?page=1&size=10&sort=createTime,desc&gender=1&keywords=张" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "X-Device-Sn: DEVICE123456"
```

### 响应示例
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "id": 1001,
        "name": "张三",
        "gender": 1,
        "birthday": "1980-01-01",
        "age": 43,
        "phoneNumber": "138****1234",
        "createTime": "2023-05-01T10:30:00Z",
        "updateTime": "2023-05-15T14:20:00Z"
      },
      {
        "id": 1002,
        "name": "张四",
        "gender": 1,
        "birthday": "1975-08-15",
        "age": 48,
        "phoneNumber": "139****5678",
        "createTime": "2023-04-28T09:15:00Z",
        "updateTime": "2023-04-28T09:15:00Z"
      }
    ],
    "total": 25
  }
}
```

## 5. 评估报告查询示例

### 请求示例
```bash
curl -X GET "https://api.hrv-system.com/re-mpd/api/assessment?page=1&size=5&sort=createTime,desc" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "X-Device-Sn: DEVICE123456"
```

### 响应示例
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "id": 2001,
        "name": "张三",
        "gender": 1,
        "age": 43,
        "createTime": "2023-05-01T10:30:00Z",
        "patientId": 1001,
        "reportUrl": "https://api.hrv-system.com/reports/RPT20231201001.html"
      },
      {
        "id": 2002,
        "name": "李四",
        "gender": 2,
        "age": 38,
        "createTime": "2023-04-30T15:45:00Z",
        "patientId": 1003,
        "reportUrl": "https://api.hrv-system.com/reports/RPT20231130002.html"
      }
    ],
    "total": 15
  }
}
```

## 6. 错误响应示例

### 401 未授权错误
```json
{
  "code": 401,
  "message": "令牌无效，请重新授权",
  "data": null,
  "timestamp": "2023-05-01T10:30:00Z"
}
```

### 400 参数错误
```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": {
    "errors": [
      {
        "field": "patientId",
        "message": "患者ID不能为空"
      },
      {
        "field": "timeDomain.meanNN",
        "message": "平均RR间期必须大于0"
      }
    ]
  },
  "timestamp": "2023-05-01T10:30:00Z"
}
```

### 429 频率限制错误
```json
{
  "code": 429,
  "message": "请求过于频繁，请 1 分钟后重试",
  "data": {
    "retryAfter": 60,
    "limit": 100,
    "remaining": 0
  },
  "timestamp": "2023-05-01T10:30:00Z"
}
```

## 7. Android客户端调用示例（Kotlin）

```kotlin
// 使用Retrofit进行API调用
class HRVApiClient {
    
    private val retrofit = Retrofit.Builder()
        .baseUrl("https://api.hrv-system.com/")
        .addConverterFactory(GsonConverterFactory.create())
        .build()
    
    private val apiService = retrofit.create(HRVApiService::class.java)
    
    // 上报评估数据
    suspend fun reportEvaluationData(
        result: AnalysisResult,
        authorization: String
    ): ApiResponse<EvaluationReportResult> {
        val requestBody = Gson().toJson(result)
            .toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
        
        return apiService.reportEvaluationData(requestBody, authorization)
    }
    
    // 查询患者信息
    suspend fun queryPatient(
        id: Long,
        authorization: String
    ): ApiResponse<Patient> {
        return apiService.queryPatient(id, authorization)
    }
}
```

## 8. 请求头说明

所有API请求都应包含以下标准请求头：

```
Content-Type: application/json;charset=UTF-8
Authorization: Bearer {access_token}
X-Device-Sn: {device_serial_number}
X-App-Version: {app_version}
X-Device-Mode: {device_model}
X-Airdoc-Client: e4d4b6c3-2b49-440e-98fd-0837a5fcb858
Accept-Language: zh-CN
```

## 9. 蓝牙设备连接API调用示例

### 9.1 蓝牙设备扫描示例

```kotlin
class BluetoothManager : BleChangeObserver {

    // 开始扫描设备
    fun startDeviceScan() {
        val models = intArrayOf(Bluetooth.MODEL_PC60FW) // PC60FW设备型号
        BleServiceHelper.BleServiceHelper.startScan(models)

        // 设置扫描超时
        Handler(Looper.getMainLooper()).postDelayed({
            stopDeviceScan()
        }, 30000) // 30秒超时
    }

    // 停止扫描
    fun stopDeviceScan() {
        BleServiceHelper.BleServiceHelper.stopScan()
    }

    // 获取发现的设备列表
    fun getDiscoveredDevices(): List<Bluetooth> {
        return BluetoothController.getDevices()
    }
}
```

### 9.2 蓝牙设备连接示例

```kotlin
class DeviceConnectionManager : BleChangeObserver {

    fun connectDevice(context: Context, bluetooth: Bluetooth) {
        try {
            // 1. 设置设备接口
            BleServiceHelper.BleServiceHelper.setInterfaces(bluetooth.model)

            // 2. 添加状态观察者
            lifecycle.addObserver(BIOL(this, intArrayOf(bluetooth.model)))

            // 3. 停止扫描
            BleServiceHelper.BleServiceHelper.stopScan()

            // 4. 连接设备
            BleServiceHelper.BleServiceHelper.connect(context, bluetooth.model, bluetooth.device)

            // 5. 清空设备控制器
            BluetoothController.clear()

            Log.d(TAG, "开始连接设备: ${bluetooth.name}")
        } catch (e: Exception) {
            Log.e(TAG, "设备连接失败: ${e.message}")
        }
    }

    override fun onBleStateChanged(model: Int, state: Int) {
        when (state) {
            Ble.State.CONNECTED -> {
                Log.d(TAG, "设备连接成功")
                handleDeviceConnected(model)
            }
            Ble.State.DISCONNECTED -> {
                Log.d(TAG, "设备断开连接")
                handleDeviceDisconnected(model)
            }
            Ble.State.CONNECT_FAILED -> {
                Log.e(TAG, "设备连接失败")
                handleConnectionFailed(model)
            }
        }
    }

    fun disconnectDevice() {
        BleServiceHelper.BleServiceHelper.disconnect(false)
    }
}
```

### 9.3 PPG数据接收示例

```kotlin
class PPGDataCollector {

    private val ppgDataPoints = mutableListOf<PPGDataPoint>()

    fun startDataCollection() {
        // 注册PPG波形数据监听
        LiveEventBus.get(InterfaceEvent.PC60FW.EventPc60FwRtWave, RtWave::class.java)
            .observe(lifecycleOwner) { rtWave ->
                handlePPGData(rtWave)
            }

        // 注册实时参数监听
        LiveEventBus.get(InterfaceEvent.PC60FW.EventPc60FwRtParam, RtParam::class.java)
            .observe(lifecycleOwner) { rtParam ->
                handleRealTimeParams(rtParam)
            }
    }

    private fun handlePPGData(rtWave: RtWave) {
        val currentTime = System.nanoTime()

        // 处理波形数据
        rtWave.wave.forEachIndexed { index, value ->
            val ppgDataPoint = PPGDataPoint(
                ppgValue = value.toDouble(),
                timestamp = currentTime + (index * 8_000_000L) // 125Hz采样率
            )
            ppgDataPoints.add(ppgDataPoint)
        }

        // 更新UI显示
        updateWaveformDisplay(rtWave.wave)

        Log.d(TAG, "接收PPG数据: ${rtWave.wave.size}个采样点, HR: ${rtWave.hr}, SpO2: ${rtWave.spo2}")
    }

    private fun handleRealTimeParams(rtParam: RtParam) {
        Log.d(TAG, "实时参数 - HR: ${rtParam.hr}, SpO2: ${rtParam.spo2}, PI: ${rtParam.pi}")
    }

    fun getPPGDataPoints(): List<PPGDataPoint> {
        return ppgDataPoints.toList()
    }

    fun clearData() {
        ppgDataPoints.clear()
    }
}
```

## 10. WiFi打印API调用示例

### 10.1 打印机发现和连接示例

```kotlin
class PrinterManager {

    private lateinit var printManager: PrintManager

    fun initializePrintManager(context: Context) {
        printManager = context.getSystemService(Context.PRINT_SERVICE) as PrintManager
    }

    // 获取可用打印机列表
    fun getAvailablePrinters(): List<PrintServiceInfo> {
        return printManager.enabledPrintServices
    }

    // 检查打印服务状态
    fun checkPrintServiceStatus(): Boolean {
        val enabledServices = printManager.enabledPrintServices
        return enabledServices.isNotEmpty()
    }
}
```

### 10.2 创建打印任务示例

```kotlin
class ReportPrintManager {

    fun printEvaluationReport(context: Context, webView: WebView) {
        val printManager = context.getSystemService(Context.PRINT_SERVICE) as PrintManager
        val jobName = "${context.getString(R.string.app_name)} - 评估报告"

        // 配置打印属性
        val printAttributes = PrintAttributes.Builder()
            .setMediaSize(PrintAttributes.MediaSize.ISO_A4)        // A4纸张
            .setColorMode(PrintAttributes.COLOR_MODE_COLOR)        // 彩色打印
            .setResolution(PrintAttributes.Resolution("pdf", "pdf", 600, 600)) // 600 DPI
            .setMinMargins(PrintAttributes.Margins.NO_MARGINS)     // 无边距
            .build()

        // 创建打印适配器
        val printAdapter = webView.createPrintDocumentAdapter(jobName)

        // 开始打印任务
        try {
            val printJob = printManager.print(jobName, printAdapter, printAttributes)

            // 监控打印状态
            monitorPrintJob(printJob)

            Log.d(TAG, "打印任务已创建: ${printJob.id}")
        } catch (e: Exception) {
            Log.e(TAG, "创建打印任务失败: ${e.message}")
            handlePrintError(e)
        }
    }

    private fun monitorPrintJob(printJob: PrintJob) {
        // 检查打印任务状态
        when {
            printJob.isQueued -> Log.d(TAG, "打印任务排队中")
            printJob.isStarted -> Log.d(TAG, "打印任务已开始")
            printJob.isCompleted -> Log.d(TAG, "打印任务已完成")
            printJob.isFailed -> Log.e(TAG, "打印任务失败")
            printJob.isCancelled -> Log.d(TAG, "打印任务已取消")
        }
    }

    private fun handlePrintError(error: Exception) {
        when (error) {
            is SecurityException -> {
                Log.e(TAG, "打印权限不足")
                // 提示用户授予打印权限
            }
            is IllegalStateException -> {
                Log.e(TAG, "打印服务不可用")
                // 提示用户检查打印机连接
            }
            else -> {
                Log.e(TAG, "打印异常: ${error.message}")
                // 通用错误处理
            }
        }
    }
}
```

### 10.3 打印状态监控示例

```kotlin
class PrintJobMonitor {

    private val printJobCallbacks = mutableMapOf<String, PrintJobCallback>()

    interface PrintJobCallback {
        fun onPrintJobQueued(jobId: String)
        fun onPrintJobStarted(jobId: String)
        fun onPrintJobCompleted(jobId: String)
        fun onPrintJobFailed(jobId: String, error: String)
        fun onPrintJobCancelled(jobId: String)
    }

    fun registerPrintJobCallback(jobId: String, callback: PrintJobCallback) {
        printJobCallbacks[jobId] = callback
    }

    fun unregisterPrintJobCallback(jobId: String) {
        printJobCallbacks.remove(jobId)
    }

    // 定期检查打印任务状态
    private fun checkPrintJobStatus(printJob: PrintJob) {
        val jobId = printJob.id.toString()
        val callback = printJobCallbacks[jobId]

        when {
            printJob.isQueued -> callback?.onPrintJobQueued(jobId)
            printJob.isStarted -> callback?.onPrintJobStarted(jobId)
            printJob.isCompleted -> {
                callback?.onPrintJobCompleted(jobId)
                unregisterPrintJobCallback(jobId)
            }
            printJob.isFailed -> {
                callback?.onPrintJobFailed(jobId, "打印失败")
                unregisterPrintJobCallback(jobId)
            }
            printJob.isCancelled -> {
                callback?.onPrintJobCancelled(jobId)
                unregisterPrintJobCallback(jobId)
            }
        }
    }
}
```

## 11. 完整的设备管理示例

```kotlin
class HRVDeviceManager : BleChangeObserver {

    private var isScanning = false
    private var isConnected = false
    private var currentDevice: Bluetooth? = null

    // 初始化设备管理器
    fun initialize(application: Application): Boolean {
        return try {
            val rawFolders = SparseArray<String>()
            rawFolders.set(Bluetooth.MODEL_PC60FW,
                "${application.getExternalFilesDir(null)?.absolutePath}/pc60fw")

            BleServiceHelper.BleServiceHelper
                .initRawFolder(rawFolders)
                .initService(application)
                .initLog(false)

            Log.d(TAG, "设备管理器初始化成功")
            true
        } catch (e: Exception) {
            Log.e(TAG, "设备管理器初始化失败: ${e.message}")
            false
        }
    }

    // 扫描并连接设备的完整流程
    fun scanAndConnect(context: Context, onDeviceFound: (List<Bluetooth>) -> Unit) {
        if (isScanning) return

        isScanning = true

        // 开始扫描
        val models = intArrayOf(Bluetooth.MODEL_PC60FW)
        BleServiceHelper.BleServiceHelper.startScan(models)

        // 定期检查发现的设备
        val handler = Handler(Looper.getMainLooper())
        val checkDevicesRunnable = object : Runnable {
            override fun run() {
                val devices = BluetoothController.getDevices()
                if (devices.isNotEmpty()) {
                    onDeviceFound(devices)
                }

                if (isScanning) {
                    handler.postDelayed(this, 2000) // 每2秒检查一次
                }
            }
        }

        handler.post(checkDevicesRunnable)

        // 30秒后停止扫描
        handler.postDelayed({
            stopScan()
        }, 30000)
    }

    fun stopScan() {
        isScanning = false
        BleServiceHelper.BleServiceHelper.stopScan()
    }

    fun connectToDevice(context: Context, device: Bluetooth) {
        currentDevice = device

        BleServiceHelper.BleServiceHelper.setInterfaces(device.model)
        lifecycle.addObserver(BIOL(this, intArrayOf(device.model)))
        BleServiceHelper.BleServiceHelper.stopScan()
        BleServiceHelper.BleServiceHelper.connect(context, device.model, device.device)
        BluetoothController.clear()
    }

    override fun onBleStateChanged(model: Int, state: Int) {
        when (state) {
            Ble.State.CONNECTED -> {
                isConnected = true
                Log.d(TAG, "设备连接成功: ${currentDevice?.name}")
            }
            Ble.State.DISCONNECTED -> {
                isConnected = false
                Log.d(TAG, "设备断开连接")
            }
            Ble.State.CONNECT_FAILED -> {
                isConnected = false
                Log.e(TAG, "设备连接失败")
            }
        }
    }

    fun disconnect() {
        if (isConnected) {
            BleServiceHelper.BleServiceHelper.disconnect(false)
        }
    }

    fun isDeviceConnected(): Boolean = isConnected
    fun getCurrentDevice(): Bluetooth? = currentDevice
}
```

## 12. 数据验证规则

### 患者信息验证
- 姓名：1-50个字符，不能为空
- 性别：1（男）或2（女）
- 生日：YYYY-MM-DD格式
- 手机号：11位数字

### HRV数据验证
- meanNN：300-2000ms范围
- SDNN：0-500ms范围
- RMSSD：0-500ms范围
- 有效间期比例：≥80%

### 蓝牙连接参数验证
- 设备型号：必须为Bluetooth.MODEL_PC60FW (0x15)
- MAC地址：格式为XX:XX:XX:XX:XX:XX
- 连接超时：5-60秒范围
- 信号强度：RSSI > -80dBm

### 打印参数验证
- 纸张规格：支持A4、Letter等标准规格
- 分辨率：300-1200 DPI范围
- 颜色模式：单色或彩色
- 文件大小：不超过50MB
