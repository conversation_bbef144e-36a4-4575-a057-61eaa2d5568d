# HRV注册App数据接口描述文档

## 一、设备端数据接口

### 1. 蓝牙设备连接接口
**接口名称：** 蓝牙设备连接接口  
**预期用户：** 设备操作者  
**使用场景：** 需要连接脉搏血氧饱和度仪进行心率变异性数据采集时  
**预期用途：** 实现与PC60FW等蓝牙设备的连接，获取PPG信号数据用于HRV分析  
**技术特征：** 通过标准蓝牙BLE协议传输，支持实时数据流传输，数据格式为PPG原始信号值  
**使用限制：** 仅支持PC60FW型号设备，需要Android 6.0以上系统，需要蓝牙和位置权限  
**故障应对措施：** 当连接中断时，自动尝试重连；连接超时30秒后提示重新连接；设备未找到时引导用户检查设备电源和距离

### 2. PPG数据采集接口
**接口名称：** PPG数据采集接口  
**预期用户：** 设备操作者  
**使用场景：** 连接设备后进行心率变异性测量时  
**预期用途：** 实时采集PPG信号数据，进行心率变异性分析计算  
**技术特征：** 采样频率125Hz，数据精度16位，支持实时波形显示和数据缓存  
**使用限制：** 测量时间需要5-10分钟，要求患者保持静止状态  
**故障应对措施：** 信号质量差时提示重新佩戴设备；数据丢失时自动补偿；异常值自动过滤

### 3. 评估报告打印接口
**接口名称：** 评估报告打印接口  
**预期用户：** 设备操作者  
**使用场景：** 评估结束需要打印报告时通过WIFI打印机打印报告  
**预期用途：** 实现评估报告打印  
**技术特征：** 通过标准WIFI协议传输，数据格式为PDF，支持A4纸张，彩色打印，600DPI分辨率  
**使用限制：** 仅当安装了对应的WIFI打印机驱动软件后才可以正常打印  
**故障应对措施：** 当打印过程中中断，进行重新连接后即可重新打印；打印机离线时提示检查网络连接；打印队列满时等待后重试

## 二、服务器端数据接口

### 1. 患者数据管理接口
**接口名称：** 患者数据管理接口  
**预期用户：** 医护人员、系统管理员  
**使用场景：** 需要管理患者基本信息和查询患者记录时  
**预期用途：** 提供患者信息的增删改查功能，支持患者档案管理  
**技术特征：** RESTful API设计，HTTPS加密传输，支持分页查询和条件筛选  
**使用限制：** 需要有效的授权令牌，单次查询最多返回100条记录  
**故障应对措施：** 令牌过期时返回401错误提示重新登录；服务器异常时返回503错误并记录日志；网络超时时自动重试3次

### 2. 评估数据上报接口
**接口名称：** 评估数据上报接口  
**预期用户：** 设备端应用程序  
**使用场景：** HRV评估完成后需要将结果上传到服务器时  
**预期用途：** 上传HRV分析结果，生成评估报告，支持数据统计分析  
**技术特征：** JSON格式数据传输，包含时域和频域参数，支持批量上传  
**使用限制：** 数据大小不超过10MB，需要包含完整的分析结果  
**故障应对措施：** 上传失败时本地缓存数据，网络恢复后自动重传；数据格式错误时返回详细错误信息；服务器繁忙时延迟重试

### 3. 评估报告生成接口
**接口名称：** 评估报告生成接口  
**预期用户：** 医护人员  
**使用场景：** 需要查看和打印患者HRV评估报告时  
**预期用途：** 根据评估数据生成标准化的医疗报告，支持在线查看和打印  
**技术特征：** 动态HTML报告生成，支持图表展示，可导出PDF格式  
**使用限制：** 报告有效期30天，超期需要重新生成  
**故障应对措施：** 报告生成失败时提示重新生成；模板加载失败时使用备用模板；数据不完整时标注缺失项目

## 三、数据流程说明

1. **设备连接流程：** 客户端应用程序通过蓝牙接口请求与脉搏血氧饱和度仪通信，用于搜索、连接、传输数据并在软件界面上更新蓝牙设备连接状态指示和脉搏波形图。

2. **数据采集流程：** 设备连接成功后，开始实时采集PPG信号，进行信号质量检测和预处理，实时显示波形图。

3. **数据分析流程：** 采集完成后，对PPG数据进行HRV分析，计算时域参数（SDNN、RMSSD、pNN50等）和频域参数（VLF、LF、HF功率等）。

4. **数据上报流程：** 分析结果通过HTTPS接口上传到服务器，服务器验证数据完整性后存储到数据库。

5. **报告生成流程：** 服务器根据分析结果生成标准化报告，支持Web查看和PDF导出。

6. **报告打印流程：** 应用程序评估报告通过WIFI连接无线打印机，用于搜索、连接、传输打印数据并通过打印机打印报告。
