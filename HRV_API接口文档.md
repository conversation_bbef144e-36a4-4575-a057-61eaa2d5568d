# HRV注册App API接口文档

## 1. 患者数据查询 API（应用程序接口）

**接口名称：** PatientDataQueryAPI  
**接口功能：** 供授权系统查询患者基本信息及检查记录  
**预期用户：** 医院信息系统（HIS）管理员、授权医疗软件开发者  
**使用场景：** 第三方医疗软件需调用本软件中的患者数据进行联合分析时  
**预期用途：** 安全传输患者脱敏信息（如姓名、ID、检查日期、检查类型），支持跨系统数据整合  

### 技术特征：
- **调用方式：** RESTful API（HTTP/HTTPS GET 请求）
- **请求URL：** `GET /re-mpd/api/patient/{id}`

#### 输入参数：
- **patientID**（路径参数，必填）：患者唯一标识（如医院就诊卡号）
- **authToken**（Header，必填）：访问令牌（有效期 2 小时）
  - 格式：`Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

#### 输出参数：
**返回格式：** JSON

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1001,
    "name": "张三",
    "gender": 1,
    "birthday": "1980-01-01",
    "age": 43,
    "phoneNumber": "138****1234",
    "createTime": "2023-05-01T10:30:00",
    "updateTime": "2023-05-15T14:20:00",
    "organizationId": 100
  }
}
```

- **通信协议：** HTTPS（TLS 1.2 及以上加密）
- **性能指标：** 支持每秒最多 10 次并发请求，响应时间≤500ms

### 使用限制：
- 仅允许院内授权 IP 地址访问（需提前在系统配置白名单）
- 禁止返回患者完整病历、诊断结果等敏感信息
- 单个令牌每小时最多查询1000次

### 故障应对措施：
- 若authToken无效/过期，返回错误码 401 及提示 "令牌无效，请重新授权"
- 若请求频率超限，返回错误码 429 及提示 "请求过于频繁，请 1 分钟后重试"
- 服务器故障时，返回错误码 503 及提示 "服务暂时不可用，请稍后重试"，并自动记录错误日志

---

## 2. 患者列表查询 API

**接口名称：** PatientListQueryAPI  
**接口功能：** 分页查询患者列表，支持条件筛选  
**预期用户：** 医护人员、系统管理员  
**使用场景：** 需要浏览和管理患者档案时  
**预期用途：** 提供患者列表的分页查询功能，支持按性别、关键词等条件筛选  

### 技术特征：
- **调用方式：** RESTful API（HTTP/HTTPS GET 请求）
- **请求URL：** `GET /re-mpd/api/patient`

#### 输入参数：
- **page**（查询参数，必填）：页码，从1开始
- **size**（查询参数，必填）：每页条数，示例值(10)
- **sort**（查询参数，可选）：排序条件，示例值(按创建时间倒序：createTime,desc)
- **gender**（查询参数，可选）：性别{0=未知, 1=男, 2=女}，可用值:0,1,2
- **keywords**（查询参数，可选）：搜索关键词，示例值(张三)
- **Authorization**（Header，必填）：授权令牌

#### 输出参数：
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "id": 1001,
        "name": "张三",
        "gender": 1,
        "birthday": "1980-01-01",
        "age": 43,
        "phoneNumber": "138****1234",
        "createTime": "2023-05-01T10:30:00",
        "updateTime": "2023-05-15T14:20:00"
      }
    ],
    "total": 150
  }
}
```

---

## 3. HRV评估数据上报 API

**接口名称：** HRVAssessmentDataAPI  
**接口功能：** 接收设备端上传的HRV评估数据并生成报告  
**预期用户：** HRV设备端应用程序  
**使用场景：** HRV测量完成后需要上传分析结果到服务器时  
**预期用途：** 接收HRV分析数据，生成标准化医疗报告，支持后续查询和打印  

### 技术特征：
- **调用方式：** RESTful API（HTTP/HTTPS POST 请求）
- **请求URL：** `POST /re-mpd/api/assessment/rppg`

#### 输入参数：
- **Authorization**（Header，必填）：授权令牌
- **Content-Type**（Header，必填）：application/json;charset=UTF-8
- **请求体**（JSON格式）：

```json
{
  "patientId": 1001,
  "timeDomain": {
    "meanNN": 850.5,
    "sdnn": 45.2,
    "rmssd": 32.8,
    "sdsd": 28.5,
    "pnn50": 15.6
  },
  "frequencyDomain": {
    "totalPower": 2500.0,
    "vlf": 800.0,
    "lf": 900.0,
    "hf": 800.0,
    "lfHfRatio": 1.125,
    "stepPower": [120.5, 135.2, 145.8, ...]
  },
  "rrIntervals": [850, 845, 860, 855, ...],
  "validIntervals": 450,
  "totalIntervals": 500
}
```

#### 输出参数：
```json
{
  "code": 200,
  "message": "上报成功",
  "data": {
    "reportId": "RPT20231201001"
  }
}
```

### 使用限制：
- 数据大小不超过10MB
- 必须包含完整的时域和频域参数
- 有效RR间期数不少于总数的80%

### 故障应对措施：
- 数据格式错误时返回400错误及详细验证信息
- 上传失败时客户端应本地缓存，网络恢复后重传
- 服务器处理异常时返回500错误并记录详细日志

---

## 4. 评估报告查询 API

**接口名称：** AssessmentReportAPI  
**接口功能：** 查询患者的HRV评估报告列表  
**预期用户：** 医护人员  
**使用场景：** 需要查看患者历史评估记录时  
**预期用途：** 提供评估报告的查询和访问功能  

### 技术特征：
- **调用方式：** RESTful API（HTTP/HTTPS GET 请求）
- **请求URL：** `GET /re-mpd/api/assessment`

#### 输入参数：
- **page**（查询参数，必填）：页码，从1开始
- **size**（查询参数，必填）：每页条数
- **sort**（查询参数，可选）：排序条件
- **gender**（查询参数，可选）：性别筛选
- **keywords**（查询参数，可选）：搜索关键词
- **Authorization**（Header，必填）：授权令牌

#### 输出参数：
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "id": 2001,
        "name": "张三",
        "gender": 1,
        "age": 43,
        "createTime": "2023-05-01T10:30:00",
        "patientId": 1001,
        "reportUrl": "https://server.com/report/RPT20231201001"
      }
    ],
    "total": 25
  }
}
```

---

## 5. 患者信息管理 API

### 5.1 添加患者 API

**接口名称：** AddPatientAPI
**接口功能：** 添加新患者信息到系统
**预期用户：** 医护人员、前台工作人员
**使用场景：** 新患者首次就诊需要建档时
**预期用途：** 创建患者档案，录入基本信息

#### 技术特征：
- **调用方式：** RESTful API（HTTP/HTTPS POST 请求）
- **请求URL：** `POST /re-mpd/api/patient`

#### 输入参数：
```json
{
  "name": "李四",
  "gender": 1,
  "birthday": "1985-03-15",
  "phoneNumber": "139****5678"
}
```

#### 输出参数：
```json
{
  "code": 200,
  "message": "添加成功",
  "data": {
    "id": "1002"
  }
}
```

### 5.2 修改患者信息 API

**接口名称：** UpdatePatientAPI
**接口功能：** 修改现有患者的基本信息
**预期用户：** 医护人员、前台工作人员
**使用场景：** 患者信息变更需要更新档案时
**预期用途：** 更新患者基本信息，保持档案准确性

#### 技术特征：
- **调用方式：** RESTful API（HTTP/HTTPS PUT 请求）
- **请求URL：** `PUT /re-mpd/api/patient/{id}`

#### 输入参数：
- **id**（路径参数，必填）：患者ID
- **Authorization**（Header，必填）：授权令牌
- **请求体**：患者信息JSON

#### 输出参数：
```json
{
  "code": 200,
  "message": "修改成功",
  "data": null
}
```

---

## 6. 用户认证 API

### 6.1 用户登录 API

**接口名称：** UserLoginAPI
**接口功能：** 用户身份验证和令牌获取
**预期用户：** 系统用户（医护人员、管理员）
**使用场景：** 用户需要登录系统进行操作时
**预期用途：** 验证用户身份，颁发访问令牌

#### 技术特征：
- **调用方式：** RESTful API（HTTP/HTTPS POST 请求）
- **请求URL：** `POST /re-mpd/api/account/login`

#### 输入参数：
```json
{
  "username": "doctor001",
  "password": "encrypted_password"
}
```

#### 输出参数：
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_string",
    "expiresIn": 7200,
    "tokenType": "Bearer"
  }
}
```

### 6.2 用户信息查询 API

**接口名称：** UserInfoAPI
**接口功能：** 获取当前登录用户的详细信息
**预期用户：** 已登录的系统用户
**使用场景：** 需要显示用户个人信息时
**预期用途：** 获取用户基本信息和权限信息

#### 技术特征：
- **调用方式：** RESTful API（HTTP/HTTPS GET 请求）
- **请求URL：** `GET /re-mpd/api/account/info`

#### 输入参数：
- **Authorization**（Header，必填）：Bearer token

#### 输出参数：
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": 1,
    "username": "doctor001",
    "name": "张医生",
    "email": "<EMAIL>",
    "gender": 1,
    "phoneNumber": "138****9999",
    "organizationId": 100,
    "status": 1
  }
}
```

---

## 7. 设备通信接口规范

### 7.1 蓝牙设备连接API

#### 7.1.1 蓝牙设备扫描API

**接口名称：** BluetoothScanAPI
**接口功能：** 扫描附近的蓝牙设备
**调用方式：** Android本地API调用

##### 输入参数：
```kotlin
fun startScan(
    models: IntArray = intArrayOf(Bluetooth.MODEL_PC60FW), // 设备型号数组
    scanTimeout: Long = 30000L, // 扫描超时时间(毫秒)
    enableFilter: Boolean = true // 是否启用设备过滤
)
```

##### 输出参数：
```kotlin
data class BluetoothDevice(
    val name: String,           // 设备名称
    val macAddr: String,        // MAC地址
    val model: Int,             // 设备型号 (Bluetooth.MODEL_PC60FW = 0x15)
    val rssi: Int,              // 信号强度
    val isConnectable: Boolean  // 是否可连接
)
```

#### 7.1.2 蓝牙设备连接API

**接口名称：** BluetoothConnectAPI
**接口功能：** 连接指定的蓝牙设备
**调用方式：** Android本地API调用

##### 输入参数：
```kotlin
fun connectDevice(
    context: Context,           // Android上下文
    model: Int,                 // 设备型号
    device: BluetoothDevice,    // 目标设备
    connectionTimeout: Long = 30000L, // 连接超时时间
    autoReconnect: Boolean = true     // 是否自动重连
)
```

##### 输出参数：
```kotlin
// 通过BleChangeObserver回调接口返回连接状态
interface BleChangeObserver {
    fun onBleStateChanged(model: Int, state: Int)
    // state值说明:
    // Ble.State.CONNECTED = 2      // 已连接
    // Ble.State.CONNECTING = 1     // 连接中
    // Ble.State.DISCONNECTED = 0   // 已断开
    // Ble.State.CONNECT_FAILED = -1 // 连接失败
}
```

#### 7.1.3 实时参数接收API

**接口名称：** RealTimeParamReceiveAPI
**接口功能：** 接收设备实时参数数据（血氧、脉率、灌注指数）
**调用方式：** 事件监听机制

##### 输入参数：
```kotlin
// 注册实时参数监听器
LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwRtParam)
    .observe(lifecycleOwner) { interfaceEvent ->
        val data = interfaceEvent.data
        if (data is RtParam) {
            // 处理实时参数数据
        }
    }
```

##### 输出参数：
```kotlin
data class RtParam(
    val spo2: Int,              // 血氧饱和度 (%)
    val pr: Int,                // 脉率 (bpm)
    val pi: Float               // 灌注指数
)
```

#### 7.1.4 PPG波形数据接收API

**接口名称：** PPGWaveDataReceiveAPI
**接口功能：** 接收设备实时PPG波形数据
**调用方式：** 事件监听机制

##### 输入参数：
```kotlin
// 注册PPG波形数据监听器
LiveEventBus.get<InterfaceEvent>(InterfaceEvent.PC60Fw.EventPC60FwRtWave)
    .observe(lifecycleOwner) { interfaceEvent ->
        val data = interfaceEvent.data
        if (data is RtWave) {
            // 处理PPG波形数据
        }
    }
```

##### 输出参数：
```kotlin
data class RtWave(
    val waveIntData: IntArray   // PPG波形数据数组，整数格式
)

// 转换为PPGDataPoint格式
data class PPGDataPoint(
    val ppgValue: Double,       // PPG信号值
    val timestamp: Long         // 时间戳（纳秒）
)

// 数据转换逻辑
val ints = data.waveIntData.toList()
ints.forEachIndexed { index, value ->
    if (ppgDataPointList.isEmpty()) {
        // 首个数据点使用当前时间戳
        ppgDataPointList.add(PPGDataPoint(
            value.toDouble(),
            System.currentTimeMillis() * 1_000_000L + System.nanoTime() % 1_000_000L
        ))
    } else {
        // 后续数据点间隔20毫秒（50Hz采样率）
        ppgDataPointList.add(PPGDataPoint(
            value.toDouble(),
            ppgDataPointList[ppgDataPointList.size - 1].timestamp + 20 * 1_000_000L
        ))
    }
}
```

#### 7.1.4 蓝牙设备断开API

**接口名称：** BluetoothDisconnectAPI
**接口功能：** 断开蓝牙设备连接
**调用方式：** Android本地API调用

##### 输入参数：
```kotlin
fun disconnectDevice(
    force: Boolean = false      // 是否强制断开
)
```

##### 输出参数：
```kotlin
// 返回断开结果
data class DisconnectResult(
    val success: Boolean,       // 是否成功断开
    val message: String         // 结果消息
)
```

### 7.2 WiFi打印机连接API

#### 7.2.1 打印机发现API

**接口名称：** PrinterDiscoveryAPI
**接口功能：** 发现网络中的可用打印机
**调用方式：** Android系统API调用

##### 输入参数：
```kotlin
fun discoverPrinters(
    context: Context,           // Android上下文
    discoveryTimeout: Long = 10000L, // 发现超时时间
    printerTypes: Array<String> = arrayOf("PDF", "IPP") // 支持的打印机类型
)
```

##### 输出参数：
```kotlin
data class PrinterInfo(
    val printerId: String,      // 打印机ID
    val name: String,           // 打印机名称
    val description: String,    // 打印机描述
    val status: Int,            // 打印机状态
    val capabilities: PrinterCapabilities // 打印机能力
)

data class PrinterCapabilities(
    val mediaSize: List<String>,    // 支持的纸张规格
    val colorModes: List<String>,   // 支持的颜色模式
    val resolutions: List<String>   // 支持的分辨率
)
```

#### 7.2.2 打印任务创建API

**接口名称：** PrintJobCreateAPI
**接口功能：** 创建打印任务
**调用方式：** Android PrintManager API

##### 输入参数：
```kotlin
fun createPrintJob(
    webView: WebView,           // 要打印的WebView
    jobName: String,            // 打印任务名称
    printAttributes: PrintAttributes // 打印属性
)

// 打印属性配置
val printAttributes = PrintAttributes.Builder()
    .setMediaSize(PrintAttributes.MediaSize.ISO_A4)        // 纸张规格
    .setColorMode(PrintAttributes.COLOR_MODE_COLOR)        // 彩色模式
    .setResolution(PrintAttributes.Resolution("pdf", "pdf", 600, 600)) // 分辨率
    .setMinMargins(PrintAttributes.Margins.NO_MARGINS)     // 边距设置
    .build()
```

##### 输出参数：
```kotlin
data class PrintJobResult(
    val jobId: String,          // 打印任务ID
    val status: String,         // 任务状态
    val message: String         // 结果消息
)

// 打印状态枚举
enum class PrintJobStatus {
    QUEUED,     // 队列中
    STARTED,    // 已开始
    COMPLETED,  // 已完成
    FAILED,     // 失败
    CANCELLED   // 已取消
}
```

#### 7.2.3 打印状态监控API

**接口名称：** PrintStatusMonitorAPI
**接口功能：** 监控打印任务状态
**调用方式：** 回调监听机制

##### 输入参数：
```kotlin
interface PrintJobCallback {
    fun onPrintJobQueued(jobId: String)
    fun onPrintJobStarted(jobId: String)
    fun onPrintJobCompleted(jobId: String)
    fun onPrintJobFailed(jobId: String, error: String)
    fun onPrintJobCancelled(jobId: String)
}

fun monitorPrintJob(
    jobId: String,              // 打印任务ID
    callback: PrintJobCallback  // 状态回调
)
```

##### 输出参数：
```kotlin
data class PrintStatusUpdate(
    val jobId: String,          // 任务ID
    val status: PrintJobStatus, // 当前状态
    val progress: Int,          // 进度百分比
    val pagesCompleted: Int,    // 已完成页数
    val totalPages: Int,        // 总页数
    val errorMessage: String?   // 错误信息（如有）
)
```

---

## 通用错误码说明：
- **200**：请求成功
- **400**：请求参数错误
- **401**：未授权或令牌过期
- **403**：权限不足
- **404**：资源不存在
- **429**：请求频率超限
- **500**：服务器内部错误
- **503**：服务暂时不可用

## 安全规范：
- 所有API调用必须使用HTTPS协议
- 访问令牌有效期为2小时，需要定期刷新
- 敏感数据传输采用AES-256加密
- 所有API调用都会记录访问日志
- 支持IP白名单限制访问
